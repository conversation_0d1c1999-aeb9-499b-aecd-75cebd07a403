import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import { z } from 'zod'

const assessmentSchema = z.object({
  studentId: z.string().optional(),
  groupId: z.string().optional(),
  testName: z.string().min(1, 'Test name is required'),
  type: z.enum(['LEVEL_TEST', 'PROGRESS_TEST', 'FINAL_EXAM', 'GROUP_TEST']),
  level: z.enum(['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']).optional(),
  score: z.number().optional(),
  maxScore: z.number().optional(),
  passed: z.boolean().default(false),
  questions: z.any().optional(),
  results: z.any().optional(),
  completedAt: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')
    const type = searchParams.get('type')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const branch = searchParams.get('branch') || 'main'

    const where: any = {
      // Filter assessments by branch directly
      branch: branch
    }

    if (studentId) {
      where.studentId = studentId
      // Additional validation that student belongs to current branch
      where.student = { id: studentId, branch: branch }
    }

    if (type) where.type = type

    // Temporarily handle missing groupId column gracefully
    let assessments
    try {
      assessments = await prisma.assessment.findMany({
        where,
        include: {
          student: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          // Temporarily comment out group relation until schema is updated
          // group: {
          //   include: {
          //     course: {
          //       select: {
          //         name: true,
          //         level: true,
          //       },
          //     },
          //   },
          // },
        },
        orderBy: { createdAt: 'desc' },
      })
    } catch (error) {
      // If groupId column doesn't exist, fetch without group relation
      const fallbackWhere: any = {}
      if (type) fallbackWhere.type = type

      assessments = await prisma.assessment.findMany({
        where: fallbackWhere,
        include: {
          student: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      })
    }

    return NextResponse.json({
      assessments,
    })
  } catch (error) {
    console.error('Error fetching assessments:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only teachers, admins, and managers can create assessments
    if (!session.user.role || !['ADMIN', 'MANAGER', 'TEACHER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = assessmentSchema.parse(body)
    const branch = body.branch || 'main'

    // Check if student exists and belongs to the correct branch (if studentId provided)
    if (validatedData.studentId) {
      const student = await prisma.student.findUnique({
        where: {
          id: validatedData.studentId,
          branch: branch
        },
      })

      if (!student) {
        return NextResponse.json({ error: 'Student not found in the specified branch' }, { status: 404 })
      }
    }

    // Check if group exists and belongs to the correct branch (if groupId provided)
    if (validatedData.groupId) {
      const group = await prisma.group.findUnique({
        where: {
          id: validatedData.groupId,
          branch: branch
        },
      })

      if (!group) {
        return NextResponse.json({ error: 'Group not found in the specified branch' }, { status: 404 })
      }
    }

    // Temporarily handle missing columns gracefully
    const { groupId, testName, ...assessmentData } = validatedData

    // Ensure required fields are provided
    if (!validatedData.studentId) {
      return NextResponse.json({ error: 'Student ID is required' }, { status: 400 })
    }
    if (!testName) {
      return NextResponse.json({ error: 'Test name is required' }, { status: 400 })
    }

    let assessment
    try {
      assessment = await prisma.assessment.create({
        data: {
          studentId: validatedData.studentId,
          testName: testName,
          type: validatedData.type,
          level: validatedData.level,
          score: validatedData.score,
          maxScore: validatedData.maxScore,
          passed: validatedData.passed,
          questions: validatedData.questions,
          results: validatedData.results,
          branch: branch,
          // Only include groupId if it exists in schema
          ...(groupId && { groupId }),
          completedAt: validatedData.completedAt ? new Date(validatedData.completedAt) : null,
        },
        include: {
          student: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          // Temporarily comment out group relation
          // group: {
          //   include: {
          //     course: {
          //       select: {
          //         name: true,
          //         level: true,
          //       },
          //     },
          //   },
          // },
        },
      })
    } catch (error) {
      // Fallback: create without new fields
      assessment = await prisma.assessment.create({
        data: {
          studentId: validatedData.studentId!,
          testName: testName!,
          type: validatedData.type,
          level: validatedData.level,
          score: validatedData.score,
          maxScore: validatedData.maxScore,
          passed: validatedData.passed,
          questions: validatedData.questions,
          results: validatedData.results,
          branch: branch,
          completedAt: validatedData.completedAt ? new Date(validatedData.completedAt) : null,
        },
        include: {
          student: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      })
    }

    // Log the activity
    await ActivityLogger.logAssessmentCompleted(
      session.user.id,
      session.user.role as Role,
      assessment.id,
      {
        type: assessment.type,
        studentName: assessment.student?.user.name || 'Unknown',
        score: assessment.score,
        passed: assessment.passed,
      },
      request
    )

    return NextResponse.json(assessment, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating assessment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
